<!-- My Packages Content -->
<div class="max-w-6xl">

  <!-- Loading State -->
  <div *ngIf="packagesLoading" class="flex justify-center items-center py-20">
    <div class="bg-slate-800/60 backdrop-blur-md border border-slate-600/30 rounded-xl p-6">
      <svg class="animate-spin h-8 w-8 text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="packagesError && !packagesLoading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 text-center mb-4">
    <p class="text-sm lg:text-base text-red-300 mb-3">{{ packagesError }}</p>
    <button
      (click)="loadMyPackages()"
      class="px-3 py-1.5 text-sm lg:text-base bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors"
    >
      Попробовать снова
    </button>
  </div>

  <!-- My Packages Content -->
  <div *ngIf="!packagesLoading && !packagesError">
    <!-- Empty State - No Packages -->
    <div *ngIf="packages.length === 0" class="py-6 lg:py-4"> 
      <h3 class="text-lg lg:text-xl font-semibold text-white mb-2">Нет активного тарифа</h3>
      <p class="text-sm lg:text-base text-gray-400 mb-4 lg:mb-6">У вас пока нет активных игровых пакетов. Перейдите на главную страницу для выбора тарифа.</p>
      <a href="/#pricing-title" 
        class="inline-flex items-center px-4 py-2 lg:px-6 lg:py-3 text-sm lg:text-base bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
      >
        <svg class="w-4 h-4 lg:w-5 lg:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
        </svg>
        Перейти на главную
      </a>
    </div>

    <!-- Package Game Selection Interface -->
    <div *ngIf="packages.length > 0 && getCurrentPackage()" class="space-y-6">
      <!-- Package Header -->
      <div class="bg-slate-800/40 backdrop-blur-sm border border-slate-700/40 rounded-lg p-6">
        <h3 class="text-xl lg:text-2xl font-bold text-white mb-2">Мой тариф «{{ getCurrentPackage()?.package_name }}»</h3>
        <p class="text-sm lg:text-base text-gray-400 mb-1">
          Тариф «{{ getCurrentPackage()?.package_name }}» дает безлимитный доступ на 30 дней для {{ (getCurrentPackage()?.remaining_slots || 0) + (getCurrentPackage()?.selected_games?.length || 0) }} игр
        </p>
        <p class="text-sm lg:text-base text-gray-400">
          Срок действия подписки: {{ formatExpirationDate(getCurrentPackage()?.expires_at || '') }}
        </p>
      </div>

      <!-- Selection Title -->
      <div class="mb-6">
        <h4 class="text-lg lg:text-xl font-semibold text-white">
          Выберите {{ getCurrentPackage()?.remaining_slots }} игры для безлимитного доступа
        </h4>
      </div>

      <!-- Available Games Grid -->
      <div class="mb-6 lg:mb-8">
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 lg:gap-6">
          <div
            *ngFor="let game of getCurrentPackage()?.available_games"
            class="game-card cursor-pointer transition-all duration-300"
            [class.selected]="isGameSelected(game.id)"
            (click)="toggleGameSelection(game.id)"
          >
            <!-- Game Image -->
            <div class="aspect-square bg-slate-700/50 rounded-lg mb-3 flex items-center justify-center relative overflow-hidden">
              <!-- Actual game image when available -->
              <img
                *ngIf="game.cover_image"
                [src]="'http://localhost:8000' + game.cover_image"
                [alt]="game.title"
                class="w-full h-full object-cover"
              />

              <!-- Placeholder when no image -->
              <div *ngIf="!game.cover_image" class="w-full h-full bg-gradient-to-br from-slate-600 to-slate-800 flex items-center justify-center">
                <svg class="w-8 h-8 lg:w-12 lg:h-12 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                </svg>
              </div>

              <!-- Selection Overlay -->
              <div class="absolute inset-0 bg-blue-600/20 opacity-0 transition-opacity duration-300" [class.opacity-100]="isGameSelected(game.id)"></div>

              <!-- Selection Indicator -->
              <div
                class="absolute top-2 right-2 w-6 h-6 rounded-full border-2 transition-all duration-300 flex items-center justify-center"
                [class]="isGameSelected(game.id) ? 'bg-blue-600 border-blue-600' : 'bg-slate-800/80 border-slate-400'"
              >
                <svg *ngIf="isGameSelected(game.id)" class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
            </div>

            <!-- Game Title -->
            <h5 class="text-sm lg:text-base font-medium text-white text-center">{{ game.title }}</h5>
          </div>
        </div>
      </div>

      <!-- Action Button -->
      <div class="flex justify-center pt-6 border-t border-slate-700/40">
        <button
          (click)="confirmGameSelection()"
          class="px-8 py-3 text-base bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors font-medium"
        >
          Подтвердить выбор ({{ selectedGameIds.length }}/{{ getCurrentPackage()?.remaining_slots || 0 }})
        </button>
      </div>
    </div>
  </div>
</div>


